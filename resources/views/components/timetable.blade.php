@php
use Carbon\Carbon;

// Process schedules efficiently
$processedSchedules = collect($schedules)->map(function ($schedule) use ($code) {
    return [
        'day' => strtolower($schedule->day_of_week),
        'start_time' => Carbon::parse($schedule->start_time),
        'end_time' => Carbon::parse($schedule->end_time),
        'room' => $schedule->room->name ?? 'TBA',
        'subject_code' => $code,
        'duration' => Carbon::parse($schedule->start_time)->diffInMinutes(Carbon::parse($schedule->end_time)),
        'formatted_time' => Carbon::parse($schedule->start_time)->format('g:i A') . ' - ' . Carbon::parse($schedule->end_time)->format('g:i A'),
    ];
})->groupBy('day');

$weekdays = [
    'monday' => 'Mon',
    'tuesday' => 'Tue',
    'wednesday' => 'Wed',
    'thursday' => 'Thu',
    'friday' => 'Fri',
    'saturday' => 'Sat'
];

$hasSchedules = $processedSchedules->isNotEmpty();
@endphp

<div class="space-y-6">
    <!-- Header Section -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Class Schedule</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $code }} - Weekly Schedule</p>
            </div>
        </div>
        @if($hasSchedules)
            <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>{{ $processedSchedules->flatten(1)->count() }} session{{ $processedSchedules->flatten(1)->count() > 1 ? 's' : '' }}</span>
            </div>
        @endif
    </div>

    @if($hasSchedules)
        <!-- Modern Card-based Schedule Display -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($weekdays as $dayKey => $dayLabel)
                @if($processedSchedules->has($dayKey))
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-200">
                        <!-- Day Header -->
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-700 dark:to-gray-600 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                            <div class="flex items-center justify-between">
                                <h4 class="font-semibold text-gray-900 dark:text-white">{{ ucfirst($dayKey) }}</h4>
                                <span class="text-xs font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-2 py-1 rounded-full">
                                    {{ $processedSchedules[$dayKey]->count() }} session{{ $processedSchedules[$dayKey]->count() > 1 ? 's' : '' }}
                                </span>
                            </div>
                        </div>

                        <!-- Schedule Sessions -->
                        <div class="p-4 space-y-3">
                            @foreach($processedSchedules[$dayKey] as $schedule)
                                <div class="relative group">
                                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white shadow-sm hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5">
                                        <!-- Time Badge -->
                                        <div class="flex items-center justify-between mb-2">
                                            <div class="flex items-center space-x-2">
                                                <svg class="w-4 h-4 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-sm font-medium">{{ $schedule['formatted_time'] }}</span>
                                            </div>
                                            <span class="text-xs bg-white/20 px-2 py-1 rounded-full">
                                                {{ $schedule['duration'] }}min
                                            </span>
                                        </div>

                                        <!-- Subject Code -->
                                        <div class="mb-2">
                                            <span class="text-lg font-bold">{{ $schedule['subject_code'] }}</span>
                                        </div>

                                        <!-- Room Info -->
                                        <div class="flex items-center space-x-2 text-sm opacity-90">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                            <span>Room {{ $schedule['room'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            @endforeach
        </div>

        <!-- Compact Summary View -->
        <div class="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                Quick Schedule Overview
            </h4>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                @foreach($weekdays as $dayKey => $dayLabel)
                    <div class="text-center p-2 rounded-lg {{ $processedSchedules->has($dayKey) ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400' }}">
                        <div class="text-xs font-medium">{{ $dayLabel }}</div>
                        @if($processedSchedules->has($dayKey))
                            <div class="text-xs mt-1">
                                {{ $processedSchedules[$dayKey]->first()['start_time']->format('g:i A') }}
                            </div>
                        @else
                            <div class="text-xs mt-1">—</div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Schedule Assigned</h3>
            <p class="text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
                This class doesn't have any scheduled sessions yet. Schedule information will appear here once assigned.
            </p>
        </div>
    @endif
</div>
